mosquitto_sub -h localhost -p 1883 -u "CYL" -P "FDA4235JHKBFSJF541KDO3NFK4" -t "device/860549070085480/upload" >> /var/log/rtk_messages.log

mosquitto_sub -h localhost -p 31883 -u CYL -P 'FDA4235JHKBFSJF541KDO3NFK4' -t 'device/860549070085480/upload' -F '%I %p'


>> /root/2025.7.18.log


mosquitto_sub -h localhost -p 31883 -u CYL -P 'FDA4235JHKBFSJF541KDO3NFK4' -t 'device/860549070085480/upload' -F '%I %p' | awk '{
    if($0 ~ /^#BESTNAV/) { buf=$0; next }   # 收到第一半
    if(buf!="")      {print buf $0; buf=""} # 收到第二半后输出
    else             {print}               # 普通行直接输出
}' >> /root/2025.7.18.log