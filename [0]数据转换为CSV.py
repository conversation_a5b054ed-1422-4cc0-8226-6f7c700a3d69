import pandas as pd
import re
import os

# ================== CONFIGURATION ==================
# 请在此处修改输入文件和输出目录
INPUT_FILE = "C:/Users/<USER>/OneDrive/NEW-AE-LSTM/2025.7.18现场实验数据/服务器端实验二.log"
OUTPUT_DIR = 'CSV_output'
# =================================================

def parse_gngga(line, timestamp):
    parts = line.split(',')
    if len(parts) < 15:
        return None
    
    last_part = parts[14].split('*')
    
    return {
        'Timestamp': timestamp,
        'ID': parts[0],
        'UTC Time': parts[1],
        'Latitude': parts[2],
        'N/S Indicator': parts[3],
        'Longitude': parts[4],
        'E/W Indicator': parts[5],
        'Position Fix Indicator': parts[6],
        'Satellites Used': parts[7],
        'HDOP': parts[8],
        'MSL Altitude': parts[9],
        'Altitude Units': parts[10],
        'Geoid Separation': parts[11],
        'Separation Units': parts[12],
        'Age of Diff. Corr.': parts[13],
        'Diff. Ref. Station ID': last_part[0],
        'Checksum': last_part[1] if len(last_part) > 1 else ''
    }

def parse_gngst(line, timestamp):
    parts = line.split(',')
    if len(parts) < 9:
        return None
        
    last_part = parts[8].split('*')

    return {
        'Timestamp': timestamp,
        'ID': parts[0],
        'UTC Time': parts[1],
        'RMS': parts[2],
        'smjr_std': parts[3],
        'smnr_std': parts[4],
        'orient': parts[5],
        'lat_std': parts[6],
        'lon_std': parts[7],
        'alt_std': last_part[0],
        'Checksum': last_part[1] if len(last_part) > 1 else ''
    }

def parse_bestnavxyza(line, timestamp):
    if ';' not in line:
        return None
        
    header_part, data_part = line.split(';', 1)
    
    # Use regex to correctly split the data part, handling the quoted station ID
    data_fields = re.split(r',(?=(?:[^"]*"[^"]*")*[^"]*$)', data_part)

    if len(data_fields) < 28:
        return None

    last_field_parts = data_fields[-1].split('*')
    data_fields[-1] = last_field_parts[0]
    crc = last_field_parts[1] if len(last_field_parts) > 1 else ''

    if len(data_fields) < 28:
        return None

    return {
        'Timestamp': timestamp,
        'Header': header_part,
        'P-sol status': data_fields[0],
        'pos type': data_fields[1],
        'P-X': data_fields[2],
        'P-Y': data_fields[3],
        'P-Z': data_fields[4],
        'P-X_std': data_fields[5],
        'P-Y_std': data_fields[6],
        'P-Z_std': data_fields[7],
        'V-sol status': data_fields[8],
        'vel type': data_fields[9],
        'V-X': data_fields[10],
        'V-Y': data_fields[11],
        'V-Z': data_fields[12],
        'V-X_std': data_fields[13],
        'V-Y_std': data_fields[14],
        'V-Z_std': data_fields[15],
        'stn_ID': data_fields[16].strip('"'), # remove quotes
        'V-latency': data_fields[17],
        'diff_age': data_fields[18],
        'sol_age': data_fields[19],
        '#SVs': data_fields[20],
        '#solnSVs': data_fields[21],
        '#ggL1': data_fields[22],
        '#solnMultiSVs': data_fields[23],
        'Reserved': data_fields[24],
        'ext_sol_stat': data_fields[25],
        'Galileo_BDS_sig_mask': data_fields[26],
        'GPS_GLONASS_sig_mask': data_fields[27],
        'CRC': crc
    }

def main():
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    gngga_data = []
    gngst_data = []
    bestnavxyza_data = []

    timestamp_pattern = re.compile(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\s(.*)')

    with open(INPUT_FILE, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            match = timestamp_pattern.match(line)
            if match:
                timestamp, data_part = match.groups()
                if data_part.startswith('$GNGGA'):
                    parsed = parse_gngga(data_part, timestamp)
                    if parsed:
                        gngga_data.append(parsed)
                elif data_part.startswith('$GNGST'):
                    parsed = parse_gngst(data_part, timestamp)
                    if parsed:
                        gngst_data.append(parsed)
                elif data_part.startswith('#BESTNAVXYZA'):
                    parsed = parse_bestnavxyza(data_part, timestamp)
                    if parsed:
                        bestnavxyza_data.append(parsed)

    if gngga_data:
        df_gngga = pd.DataFrame(gngga_data)
        df_gngga.to_csv(os.path.join(OUTPUT_DIR, 'GNGGA.csv'), index=False, encoding='utf-8-sig')
        print(f"Successfully created {os.path.join(OUTPUT_DIR, 'GNGGA.csv')}")

    if gngst_data:
        df_gngst = pd.DataFrame(gngst_data)
        df_gngst.to_csv(os.path.join(OUTPUT_DIR, 'GNGST.csv'), index=False, encoding='utf-8-sig')
        print(f"Successfully created {os.path.join(OUTPUT_DIR, 'GNGST.csv')}")

    if bestnavxyza_data:
        df_bestnavxyza = pd.DataFrame(bestnavxyza_data)
        df_bestnavxyza.to_csv(os.path.join(OUTPUT_DIR, 'BESTNAVXYZA.csv'), index=False, encoding='utf-8-sig')
        print(f"Successfully created {os.path.join(OUTPUT_DIR, 'BESTNAVXYZA.csv')}")


if __name__ == '__main__':
    main()
