import pandas as pd
import pymap3d as pm
import matplotlib.pyplot as plt
from datetime import datetime, timedelta, date
import numpy as np
import matplotlib.dates as mdates
import os

# --- 核心配置参数 ---
# CSV数据源目录
CSV_INPUT_DIR = 'CSV_output'
# 稳定期：脚本将忽略数据采集开始后的这段时间，单位：秒
STABILIZATION_SECONDS = 60
# 基准计算期：在稳定期结束后，取这段时间的数据计算平均值，作为基准点，单位：秒
AVERAGING_SECONDS = 60

# --- 高分辨率图像输出配置 ---
# 输出图像格式配置（为MLLM分析优化）
OUTPUT_FORMATS = {
    'png': {'dpi': 200, 'description': '超高分辨率PNG位图（600 DPI）'},
    'svg': {'dpi': None, 'description': '矢量图（SVG格式，无损缩放，MLLM友好）'}
}
# 图像尺寸配置 (英寸) - 增大尺寸以显示更多细节
FIGURE_SIZE = (24, 16)
# 字体和线条配置 - 为高分辨率优化
FONT_SIZES = {
    'title': 24,
    'subplot_title': 18,
    'axis_label': 16,
    'legend': 14,
    'tick_label': 12
}
LINE_WIDTH = 0.8  # 稍微增加线宽以适应高分辨率
# Y轴填充因子：在数据范围基础上增加的额外边距，0.2表示上下各增加10%
Y_AXIS_PADDING_FACTOR = 0.5
# --- 配置结束 ---

def load_data_from_csvs():
    """
    从CSV文件夹加载和处理数据。
    优先加载 BESTNAVXYZA.csv。如果不存在，则回退到 GNGGA.csv。
    返回一个包含时间戳和ECEF坐标的DataFrame。
    """
    bestnav_path = os.path.join(CSV_INPUT_DIR, 'BESTNAVXYZA.csv')
    gngga_path = os.path.join(CSV_INPUT_DIR, 'GNGGA.csv')

    ecef_df = None

    if os.path.exists(bestnav_path):
        print(f"检测到高精度 BESTNAVXYZA.csv，将使用此文件: '{bestnav_path}'")
        df = pd.read_csv(bestnav_path)
        
        # 将时间戳字符串转换为datetime对象
        df['timestamp'] = pd.to_datetime(df['Timestamp'])
        
        # 根据 'pos type' 筛选数据，非 NARROW_INT 解的坐标设置为 NaN
        is_narrow_int = df['pos type'] == 'NARROW_INT'
        df['x'] = np.where(is_narrow_int, df['P-X'], np.nan)
        df['y'] = np.where(is_narrow_int, df['P-Y'], np.nan)
        df['z'] = np.where(is_narrow_int, df['P-Z'], np.nan)
        
        ecef_df = df[['timestamp', 'x', 'y', 'z']]

    elif os.path.exists(gngga_path):
        print(f"未找到 BESTNAVXYZA.csv，回退使用 GNGGA.csv: '{gngga_path}'")
        df = pd.read_csv(gngga_path, low_memory=False)

        # 将时间戳字符串转换为datetime对象
        df['timestamp'] = pd.to_datetime(df['Timestamp'])

        # 只处理RTK固定解 (状态4)
        is_rtk_fixed = df['Position Fix Indicator'] == 4
        
        # 将度分格式的经纬度转换为十进制度
        def ddm_to_deg(ddm):
            ddm = pd.to_numeric(ddm, errors='coerce')
            return (ddm // 100) + (ddm % 100) / 60

        lat_deg = ddm_to_deg(df['Latitude'])
        lon_deg = ddm_to_deg(df['Longitude'])

        # 考虑南纬和西经
        lat_deg = np.where(df['N/S Indicator'] == 'S', -lat_deg, lat_deg)
        lon_deg = np.where(df['E/W Indicator'] == 'W', -lon_deg, lon_deg)
        
        # 计算椭球高度(HAE) = 海拔高度(MSL) + 大地水准面差距
        alt_msl = pd.to_numeric(df['MSL Altitude'], errors='coerce')
        geoid_sep = pd.to_numeric(df['Geoid Separation'], errors='coerce').fillna(0)
        alt = alt_msl + geoid_sep

        # 将大地坐标转换为ECEF坐标
        x, y, z = pm.geodetic2ecef(lat_deg, lon_deg, alt, deg=True)

        # 只保留RTK固定解的坐标
        df['x'] = np.where(is_rtk_fixed, x, np.nan)
        df['y'] = np.where(is_rtk_fixed, y, np.nan)
        df['z'] = np.where(is_rtk_fixed, z, np.nan)
        
        ecef_df = df[['timestamp', 'x', 'y', 'z']]

    else:
        print(f"错误: 在 '{CSV_INPUT_DIR}' 目录中未找到 'BESTNAVXYZA.csv' 或 'GNGGA.csv'。")
        return None

    return ecef_df.set_index('timestamp').sort_index()


def main():
    """
    主函数，执行以下步骤:
    1. 从CSV文件加载解析后的RTK数据。
    2. 根据配置计算并建立一个稳定的参考基准点。
    3. 将所有ECEF坐标转换为相对于基准点的ENU坐标。
    4. 将位移单位转换为毫米。
    5. 生成并显示E、N、U三个方向的位移时序图。
    """
    print(f"正在从 '{CSV_INPUT_DIR}' 文件夹中读取CSV数据...")
    df = load_data_from_csvs()

    if df is None or df.empty:
        print("错误: 加载数据失败或数据为空。请检查CSV文件是否存在且包含有效数据。")
        return

    # 统计有效固定解的数量
    valid_count = df[['x', 'y', 'z']].notna().all(axis=1).sum()
    total_count = len(df)
    print(f"成功加载 {total_count} 条记录，其中 {valid_count} 条为高质量固定解记录。")

    # --- 步骤 1: 建立参考基准点 (只使用有效的固定解数据) ---
    start_time = df.index[0]
    ref_start_time = start_time + timedelta(seconds=STABILIZATION_SECONDS)
    ref_end_time = ref_start_time + timedelta(seconds=AVERAGING_SECONDS)

    print(f"数据起始时间: {start_time}")
    print(f"基准点计算窗口: 从 {ref_start_time} 到 {ref_end_time}")

    # 筛选出用于计算基准的数据 (只使用有效的固定解)
    ref_df = df[(df.index >= ref_start_time) & (df.index <= ref_end_time)]
    ref_df_valid = ref_df.dropna()  # 只使用有效数据计算基准点

    if ref_df_valid.empty:
        print("错误: 没有足够的有效固定解数据来建立参考基准点。")
        print(f"数据时间范围从 {df.index.min()} 到 {df.index.max()}。")
        print(f"基准点计算窗口为 {ref_start_time} 至 {ref_end_time}。")
        print("请检查：\n1. 数据文件的时间跨度是否足够。\n2. STABILIZATION_SECONDS 和 AVERAGING_SECONDS 参数是否过大。\n3. 数据质量是否过差，导致窗口内无固定解。")
        return

    # 计算基准点的平均ECEF坐标 (x0, y0, z0)
    x0, y0, z0 = ref_df_valid[['x', 'y', 'z']].mean()

    # 将ECEF基准点转换为大地坐标 (纬度, 经度, 高程)，这将是ENU坐标系的原点
    lat0, lon0, h0 = pm.ecef2geodetic(x0, y0, z0, deg=True)
    print(f"已建立参考基准点 (大地坐标): 纬度={lat0:.8f}°, 经度={lon0:.8f}°, 高程={h0:.4f} m")

    # --- 步骤 2: 计算每个点相对于基准点的ECEF差值向量 ---
    df['dx'] = df['x'] - x0
    df['dy'] = df['y'] - y0
    df['dz'] = df['z'] - z0

    # --- 步骤 3: 将ECEF差值向量转换为ENU坐标 (只对有效数据进行转换) ---
    def convert_to_enu(row):
        if pd.isna(row['dx']) or pd.isna(row['dy']) or pd.isna(row['dz']):
            return pd.Series([np.nan, np.nan, np.nan])
        else:
            return pd.Series(pm.ecef2enuv(row['dx'], row['dy'], row['dz'], lat0, lon0, deg=True))
    
    enu_coords = df.apply(convert_to_enu, axis=1)
    enu_coords.columns = ['e', 'n', 'u']

    # --- 步骤 4: 将单位从米转换为毫米 ---
    df['e_mm'] = enu_coords['e'] * 1000
    df['n_mm'] = enu_coords['n'] * 1000
    df['u_mm'] = enu_coords['u'] * 1000

    # --- 新增步骤: 计算二维和三维空间位移 ---
    print("正在计算二维和三维空间位移...")
    df['displacement_2d_mm'] = np.sqrt(df['e_mm']**2 + df['n_mm']**2)
    df['displacement_3d_mm'] = np.sqrt(df['e_mm']**2 + df['n_mm']**2 + df['u_mm']**2)

    print("数据处理完成，正在生成高分辨率可视化图表...")

    # --- 步骤 5: 高分辨率可视化 (为MLLM分析优化) ---
    # 设置matplotlib参数以优化高分辨率输出
    plt.rcParams.update({
        'figure.dpi': 100,  # 显示DPI
        'savefig.dpi': 600,  # 保存DPI
        'font.size': FONT_SIZES['tick_label'],
        'axes.titlesize': FONT_SIZES['subplot_title'],
        'axes.labelsize': FONT_SIZES['axis_label'],
        'xtick.labelsize': FONT_SIZES['tick_label'],
        'ytick.labelsize': FONT_SIZES['tick_label'],
        'legend.fontsize': FONT_SIZES['legend']
    })
    
    # 定义统一的绘图配置（包含新的柔和色系）
    plot_configs = {
        'e_mm': {'title': 'East-West Displacement (E)', 'label': 'East (E) Displacement', 'color': '#4682B4', 'filename': 'displacement_e'},
        'n_mm': {'title': 'North-South Displacement (N)', 'label': 'North (N) Displacement', 'color': '#8FBC8F', 'filename': 'displacement_n'},
        'u_mm': {'title': 'Vertical Displacement (U)', 'label': 'Up (U) Displacement', 'color': '#F4A460', 'filename': 'displacement_u'},
        'displacement_2d_mm': {'title': '2D Planar Displacement (sqrt(E² + N²))', 'label': '2D Planar Displacement', 'color': '#778899', 'filename': 'displacement_2d'},
        'displacement_3d_mm': {'title': '3D Spatial Displacement (sqrt(E² + N² + U²))', 'label': '3D Spatial Displacement', 'color': '#BC8F8F', 'filename': 'displacement_3d'}
    }

    # --- 5合1总图 ---
    fig, axes = plt.subplots(5, 1, figsize=(FIGURE_SIZE[0], 28), sharex=False)
    fig.suptitle('Scaffolding Displacement Monitoring (ENU & Spatial Displacements)', 
                 fontsize=FONT_SIZES['title'], y=0.96, fontweight='bold')

    # 通过循环配置来创建子图，确保颜色和样式统一
    for i, (column, config) in enumerate(plot_configs.items()):
        ax = axes[i]
        ax.plot(df.index, df[column], label=config['label'], 
                color=config['color'], linestyle='-', linewidth=LINE_WIDTH, alpha=0.8)
        ax.set_ylabel('Displacement (mm)', fontsize=FONT_SIZES['axis_label'])
        ax.set_title(config['title'], fontsize=FONT_SIZES['subplot_title'], pad=20)
        ax.grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
        ax.legend(fontsize=FONT_SIZES['legend'])
        ax.tick_params(axis='both', which='major', labelsize=FONT_SIZES['tick_label'])

    # 只为最下面的子图设置X轴标签
    axes[-1].set_xlabel('Timestamp', fontsize=FONT_SIZES['axis_label'])

    # --- Y轴范围优化 (为每个子图应用) ---
    for i, col in enumerate(plot_configs.keys()):
        if not df[col].dropna().empty:
            y_min, y_max = df[col].min(), df[col].max()
            y_range = y_max - y_min if y_max > y_min else 1
            padding = y_range * Y_AXIS_PADDING_FACTOR / 2 # 上下各增加10%
            axes[i].set_ylim(y_min - padding, y_max + padding)

    # --- X轴刻度优化 (为每个子图应用) ---
    for ax in axes:
        # 设置主刻度为每5分钟一个
        ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
        # 设置主刻度的格式为 '时:分'
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        # 为更密集的视图添加次刻度（每分钟）
        ax.xaxis.set_minor_locator(mdates.MinuteLocator())
        # 旋转标签以防重叠
        for label in ax.get_xticklabels(which='major'):
            label.set(rotation=45, horizontalalignment='right')

    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    # --- 多格式高分辨率保存 ---
    print("正在保存5合1组合图像文件...")
    output_dir = 'ENU-RESULT'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出目录: '{output_dir}'")
        
    base_filename = 'scaffolding_displacement_summary'
    
    for format_name, config in OUTPUT_FORMATS.items():
        output_filename = os.path.join(output_dir, f"{base_filename}.{format_name}")
        
        save_kwargs = {'bbox_inches': 'tight', 'pad_inches': 0.2, 'facecolor': 'white', 'edgecolor': 'none'}
        
        if config['dpi'] is not None:
            save_kwargs['dpi'] = config['dpi']
        
        plt.savefig(output_filename, format=format_name, **save_kwargs)
        print(f"✓ {config['description']} 已保存为 '{output_filename}'")
    
    # --- 步骤 6: 为每个分量单独生成并保存高分辨率图像 ---
    print("\n正在为每个位移分量生成并保存单独的高分辨率图像...")
    
    SINGLE_PLOT_FIG_SIZE = (24, 10)

    for column, config in plot_configs.items():
        plt.figure(figsize=SINGLE_PLOT_FIG_SIZE)
        
        ax_single = plt.gca()
        ax_single.plot(df.index, df[column], label=config['label'], color=config['color'], linestyle='-', linewidth=LINE_WIDTH, alpha=0.8)
        
        ax_single.set_title(config['title'], fontsize=FONT_SIZES['subplot_title'], pad=20)
        ax_single.set_ylabel('Displacement (mm)', fontsize=FONT_SIZES['axis_label'])
        ax_single.set_xlabel('Timestamp', fontsize=FONT_SIZES['axis_label'])
        ax_single.grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
        ax_single.legend(fontsize=FONT_SIZES['legend'])
        ax_single.tick_params(axis='both', which='major', labelsize=FONT_SIZES['tick_label'])

        # --- Y轴范围优化 ---
        if not df[column].dropna().empty:
            y_min, y_max = df[column].min(), df[column].max()
            y_range = y_max - y_min if y_max > y_min else 1
            padding = y_range * Y_AXIS_PADDING_FACTOR / 2
            ax_single.set_ylim(y_min - padding, y_max + padding)

        ax_single.xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
        ax_single.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax_single.xaxis.set_minor_locator(mdates.MinuteLocator())
        plt.setp(ax_single.get_xticklabels(), rotation=45, ha='right')

        plt.tight_layout(pad=1.5)

        for format_name, format_config in OUTPUT_FORMATS.items():
            output_filename = os.path.join(output_dir, f"{config['filename']}.{format_name}")
            save_kwargs = {'bbox_inches': 'tight', 'pad_inches': 0.2, 'facecolor': 'white', 'edgecolor': 'none'}
            if format_config['dpi'] is not None:
                save_kwargs['dpi'] = format_config['dpi']
            plt.savefig(output_filename, format=format_name, **save_kwargs)
            print(f"✓ 单独图像 ({format_config['description']}) 已保存为 '{output_filename}'")
        
    # 添加数据质量统计信息
    valid_ratio = (valid_count / total_count) * 100 if total_count > 0 else 0
    print(f"\n📊 数据质量统计:")
    print(f"   总记录数: {total_count}")
    print(f"   固定解记录数: {valid_count}")
    print(f"   数据质量率: {valid_ratio:.1f}%")
    
    # 计算位移统计
    if not df['e_mm'].isna().all():
        print(f"\n📏 位移统计 (毫米):")
        print(f"   东西方向 (E): 范围 [{df['e_mm'].min():.2f}, {df['e_mm'].max():.2f}], 标准差 {df['e_mm'].std():.2f}")
        print(f"   南北方向 (N): 范围 [{df['n_mm'].min():.2f}, {df['n_mm'].max():.2f}], 标准差 {df['n_mm'].std():.2f}")
        print(f"   垂直方向 (U): 范围 [{df['u_mm'].min():.2f}, {df['u_mm'].max():.2f}], 标准差 {df['u_mm'].std():.2f}")
        print(f"   二维水平位移: 范围 [{df['displacement_2d_mm'].min():.2f}, {df['displacement_2d_mm'].max():.2f}], 标准差 {df['displacement_2d_mm'].std():.2f}")
        print(f"   三维空间位移: 范围 [{df['displacement_3d_mm'].min():.2f}, {df['displacement_3d_mm'].max():.2f}], 标准差 {df['displacement_3d_mm'].std():.2f}")
    
    print(f"\n🎯 图像已针对MLLM分析进行优化，建议使用SVG格式进行细节分析。")
    
    # 显示图像
    print("\n所有图像已保存。在脚本执行结束后，将显示所有生成的交互式图表。")
    plt.show()

if __name__ == '__main__':
    main()
