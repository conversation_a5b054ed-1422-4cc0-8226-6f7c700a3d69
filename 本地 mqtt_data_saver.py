import paho.mqtt.client as mqtt
import os
import re
from datetime import datetime

# MQTT配置1
#broker_address = "mqtt.wit-motion.cn"
#broker_port = 31883
#username = "CYL"
#password = "FDA4235JHKBFSJF541KDO3NFK4"
#subid = "860549070085480"
#subtopic = "device/" + subid + "/upload"

# MQTT配置2
broker_address = "***********"
broker_port = 31883
username = "CYL"
password = "FDA4235JHKBFSJF541KDO3NFK4"
subtopic = "device/860549070085480/upload"

# 文件保存配置
LOG_FILE = "mqtt_messages.txt"
MAX_FILE_SIZE = 1024 * 1024
BACKUP_COUNT = 3

def setup_log_file():
    """初始化日志文件，处理文件大小和备份"""
    if os.path.exists(LOG_FILE) and os.path.getsize(LOG_FILE) > MAX_FILE_SIZE:
        if os.path.exists(f"{LOG_FILE}.{BACKUP_COUNT}"):
            os.remove(f"{LOG_FILE}.{BACKUP_COUNT}")
        
        for i in range(BACKUP_COUNT - 1, 0, -1):
            src = f"{LOG_FILE}.{i}"
            dst = f"{LOG_FILE}.{i + 1}"
            if os.path.exists(src):
                os.rename(src, dst)
        
        if os.path.exists(LOG_FILE):
            os.rename(LOG_FILE, f"{LOG_FILE}.1")
        print("日志文件已滚动备份")

def save_message_to_file(message):
    """处理并保存消息到文件（无换行）"""
    # 1. 删除所有换行符
    processed = message.replace('\n', '')
    # 2. 直接写入不换行
    with open(LOG_FILE, "a", encoding="utf-8") as f:
        f.write(processed)

def on_connect(client, userdata, flags, rc, properties=None):
    print(f"已连接至MQTT服务器，开始接收数据...")
    client.subscribe(subtopic)

def on_message(client, userdata, msg):
    try:
        # 首先尝试UTF-8解码
        payload = msg.payload.decode("utf-8")
        save_message_to_file(payload)
    except UnicodeDecodeError:
        # 如果UTF-8解码失败，说明是二进制数据
        # 将二进制数据转换为十六进制格式
        hex_payload = msg.payload.hex()
        formatted_hex = " ".join([hex_payload[i:i+2] for i in range(0, len(hex_payload), 2)])
        binary_message = f"[BINARY_DATA]: {formatted_hex}\n"
        save_message_to_file(binary_message)
    except Exception as e:
        print(f"处理消息出错: {e}")
        # 如果有其他错误，也尝试保存原始字节数据
        try:
            hex_payload = msg.payload.hex()
            error_message = f"[ERROR_DATA]: {hex_payload}\n"
            save_message_to_file(error_message)
        except:
            print("无法保存错误数据")

def main():
    setup_log_file()
    client = mqtt.Client()
    client.on_connect = on_connect
    client.on_message = on_message
    client.username_pw_set(username, password)
    
    try:
        client.connect(broker_address, broker_port, 60)
        print(f"正在连接至 {broker_address}:{broker_port}...")
        client.loop_forever()
    except Exception as e:
        print(f"连接失败: {e}")
    finally:
        client.disconnect()

if __name__ == "__main__":
    main()