
import re
import datetime
import warnings
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np

# Suppress numpy warning about comparing str with float, which can happen with bad data
warnings.filterwarnings('ignore', r'.*elementwise comparison failed.*')

# --- CONFIGURATION ---
INPUT_DATA_FILE = "服务器端实验一.txt"  # <--- 请将此路径修改为您的数据文件路径

# --- PARSING FUNCTIONS ---

def parse_line(line, last_date):
    """Parses a single line of GNSS data."""
    line = line.strip()
    if not line:
        return None, last_date

    # Regex to find an optional timestamp at the beginning of the line
    timestamp_match = re.match(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\s+', line)
    current_date = last_date
    if timestamp_match:
        dt_str = timestamp_match.group(1)
        line = line[timestamp_match.end():] # Line without timestamp
        current_date = datetime.datetime.strptime(dt_str, "%Y-%m-%dT%H:%M:%S").date()

    if '$GNGGA' in line:
        return parse_gngga(line, current_date), current_date
    elif '#BESTNAVXYZA' in line:
        return parse_bestnavxyza(line, current_date), current_date
    
    return None, current_date


def parse_gngga(line, current_date):
    """Parses a GNGGA message, keeping raw values."""
    parts = line.split('*')[0].split(',')
    if len(parts) < 11 or parts[0] != '$GNGGA' or not parts[2] or not parts[4] or not parts[9]:
        return None
        
    try:
        # Get UTC time from message and combine with date
        utc_time_str = parts[1]
        if current_date is None:
             print(f"警告: GNGGA 消息缺少日期信息，将使用当前日期。行: {line}")
             current_date = datetime.date.today()
        
        h, m, s_full = int(utc_time_str[0:2]), int(utc_time_str[2:4]), float(utc_time_str[4:])
        s, ms = int(s_full), int((s_full - int(s_full)) * 1_000_000)
        
        dt_obj = datetime.datetime.combine(current_date, datetime.time(h, m, s, ms))
        # Convert to UTC+8
        dt_obj_utc8 = dt_obj + datetime.timedelta(hours=8)

        fix_quality = int(parts[6])
        is_fixed = (fix_quality == 4) # 4 = RTK Fixed

        return {
            'type': 'GNGGA',
            'datetime': dt_obj_utc8,
            'lat': float(parts[2]), # Raw latitude (ddmm.mmmm)
            'lon': float(parts[4]), # Raw longitude (dddmm.mmmm)
            'alt': float(parts[9]),
            'is_fixed': is_fixed
        }
    except (ValueError, IndexError) as e:
        print(f"解析 GNGGA 行时出错: {line}\n错误: {e}")
        return None

def parse_bestnavxyza(line, current_date):
    """Parses a #BESTNAVXYZA message, keeping raw ECEF values."""
    try:
        # Split header and data parts
        header_part, data_part = line.split(';')
        
        # Data part is what we are primarily interested in
        data_fields = data_part.split(',')
        
        # According to format, fields for position type and coordinates
        pos_type = data_fields[1]
        p_sol_status = data_fields[0]
        
        # 'NARROW_INT' is the best quality, equivalent to RTK Fixed
        is_fixed = (p_sol_status == 'SOL_COMPUTED' and pos_type == 'NARROW_INT')

        x, y, z = float(data_fields[2]), float(data_fields[3]), float(data_fields[4])

        if current_date is None:
            return None
        
        # Create a placeholder datetime. It will be back-filled later.
        dt_obj = datetime.datetime.combine(current_date, datetime.time(0,0,0)) # Placeholder time

        return {
            'type': 'BESTNAVXYZA',
            'datetime': dt_obj, # This is a placeholder
            'x': x,
            'y': y,
            'z': z,
            'is_fixed': is_fixed
        }
    except (ValueError, IndexError) as e:
        print(f"解析 BESTNAVXYZA 行时出错: {line}\n错误: {e}")
        return None

# --- PLOTTING FUNCTION ---

def plot_data(df, title_prefix, columns, y_labels):
    """Generate plots for given columns."""
    if df.empty:
        print(f"{title_prefix}: 没有数据可供绘图。")
        return

    # Separate fixed and non-fixed solutions for plotting
    fixed_df = df[df['is_fixed'] == True]
    non_fixed_df = df[df['is_fixed'] == False]
    
    # --- Create Subplots ---
    fig, axes = plt.subplots(3, 1, figsize=(15, 12), sharex=True)
    fig.suptitle(f'{title_prefix} 数据分析 (原始值)', fontsize=16)

    for i in range(3):
        ax = axes[i]
        col_name = columns[i]
        ax.plot(fixed_df['datetime'], fixed_df[col_name], '.', markersize=3, label='固定解')
        ax.plot(non_fixed_df['datetime'], non_fixed_df[col_name], 'r.', markersize=5, label='非固定解')
        ax.set_ylabel(y_labels[i])
        ax.legend()
        ax.grid(True)
        # Use plain format for y-axis to avoid scientific notation
        ax.ticklabel_format(style='plain', axis='y', useOffset=False)


    # --- Formatting X-axis ---
    axes[2].set_xlabel('时间 (UTC+8)')
    axes[2].xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
    axes[2].xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
    fig.autofmt_xdate()

    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.show()

# --- MAIN LOGIC ---

def main():
    """Main function to run the analysis."""
    print(f"正在从文件 '{INPUT_DATA_FILE}' 读取数据...")
    
    all_data = []
    last_date = None
    try:
        with open(INPUT_DATA_FILE, 'r', encoding='utf-8') as f:
            for line in f:
                parsed_data, last_date = parse_line(line, last_date)
                if parsed_data:
                    all_data.append(parsed_data)
    except FileNotFoundError:
        print(f"错误: 文件 '{INPUT_DATA_FILE}' 未找到。请检查文件路径配置。")
        return
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return

    if not all_data:
        print("未能在文件中解析出任何有效数据。")
        return

    # Convert to DataFrame
    df = pd.DataFrame(all_data)
    df['datetime'] = pd.to_datetime(df['datetime'])

    # BESTNAVXYZA often doesn't have its own timestamp. Let's forward-fill from GNGGA.
    # This assumes BESTNAVXYZA messages appear right after their corresponding GNGGA.
    gngga_times = df[df['type']=='GNGGA'][['datetime']].rename(columns={'datetime':'gngga_time'})
    df = pd.concat([df, gngga_times], axis=1).ffill()
    df.loc[df['type'] == 'BESTNAVXYZA', 'datetime'] = df['gngga_time']
    df = df.drop(columns=['gngga_time'])

    gngga_df = df[df['type'] == 'GNGGA'].copy()
    bestnav_df = df[df['type'] == 'BESTNAVXYZA'].copy()

    # --- Report on Non-Fixed Solutions ---
    for name, data_df in [('GNGGA', gngga_df), ('BESTNAVXYZA', bestnav_df)]:
        if data_df.empty:
            continue
        non_fixed_count = len(data_df[data_df['is_fixed'] == False])
        total_count = len(data_df)
        if total_count > 0:
            print(f"\n--- {name} 报告 ---")
            print(f"总数据点: {total_count}")
            print(f"非固定解数量: {non_fixed_count} (占 {non_fixed_count/total_count:.2%})")

            if non_fixed_count > 0:
                non_fixed_times = data_df[data_df['is_fixed'] == False]['datetime']
                print("非固定解分布的时间范围:")
                # Group consecutive non-fixed points by finding time gaps larger than 2 seconds
                time_diffs = non_fixed_times.diff().dt.total_seconds().fillna(0)
                time_groups = (time_diffs > 2).cumsum()
                for _, group in non_fixed_times.groupby(time_groups):
                    print(f"  - 从 {group.min().strftime('%Y-%m-%d %H:%M:%S')} 到 {group.max().strftime('%Y-%m-%d %H:%M:%S')}")

    # --- Plotting ---
    plt.rcParams['font.sans-serif'] = ['SimHei'] # Use a font that supports Chinese characters
    plt.rcParams['axes.unicode_minus'] = False  # Fix for displaying minus sign

    plot_data(gngga_df, 'GNGGA', 
              columns=['lat', 'lon', 'alt'], 
              y_labels=['纬度 (ddmm.mmmm)', '经度 (dddmm.mmmm)', '高程 (米)'])
    
    plot_data(bestnav_df, 'BESTNAVXYZA', 
              columns=['x', 'y', 'z'], 
              y_labels=['X 坐标 (米)', 'Y 坐标 (米)', 'Z 坐标 (米)'])


if __name__ == '__main__':
    main() 