from openai import OpenAI
import base64

openai_api_key = "token-abc123" # your api key set in launch server
openai_api_base = "http://8.218.53.24:20002/v1" # http id
client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)

# 获取服务器上可用的模型
try:
    models = client.models.list()
    model_name = models.data[0].id if models.data else "auto"
    print(f"使用模型: {model_name}")
except:
    model_name = "auto"  # 如果获取失败，使用默认值

# 用于传本地图片
with open('1.jpg','rb') as file:
    image = "data:image/jpeg;base64,"+ base64.b64encode(file.read()).decode('utf-8')

chat_response = client.chat.completions.create(
    model=model_name,  # 使用配置的模型名称
    messages=[{
        "role": "user",
        "content": [
            # NOTE: 使用图像令牌 <image> 的提示格式是不必要的，因为提示将由API服务器自动处理。
            # 由于提示将由API服务器自动处理，因此不需要使用包含 <image> 图像令牌的提示格式。
            {"type": "text", "text": ("Detect points of anomalies in this time series, in terms of the x-axis coordinate. List one by one in a list. For example, if points x=2, 51, and 106 are anomalies, then output ”[2, 51, 106]”. If there are no anomalies, answer with an empty list [].")},
            {
                "type": "image_url",
                "image_url": {
                    "url": image,
                },
            },
        ],
    }],
)

# 优化的输出格式
print("=" * 50)
print(" AI 回复:")
print("-" * 30)
print(chat_response.choices[0].message.content)
print("-" * 30)
print(f" 模型: {chat_response.model}")
if chat_response.usage:
    print(f" Token使用: {chat_response.usage.total_tokens} (输入: {chat_response.usage.prompt_tokens}, 输出: {chat_response.usage.completion_tokens})")
else:
    print(" Token使用: 信息不可用")
print("=" * 50)