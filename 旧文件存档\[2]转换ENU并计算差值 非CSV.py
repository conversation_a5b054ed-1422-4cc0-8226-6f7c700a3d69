import pandas as pd
import pymap3d as pm
import matplotlib.pyplot as plt
from datetime import datetime, timedelta, date
import numpy as np
import matplotlib.dates as mdates
import os

# --- 核心配置参数 ---
# 数据源文件
RAW_DATA_FILE = 'C:/Users/<USER>/OneDrive/NEW-AE-LSTM/2025.7.18现场实验/服务器端实验一 修正数据.txt'
# 稳定期：脚本将忽略数据采集开始后的这段时间，单位：秒
STABILIZATION_SECONDS = 60
# 基准计算期：在稳定期结束后，取这段时间的数据计算平均值，作为基准点，单位：秒
AVERAGING_SECONDS = 60

# --- 高分辨率图像输出配置 ---
# 输出图像格式配置（为MLLM分析优化）
OUTPUT_FORMATS = {
    'png': {'dpi': 200, 'description': '超高分辨率PNG位图（600 DPI）'},
    'svg': {'dpi': None, 'description': '矢量图（SVG格式，无损缩放，MLLM友好）'}
}
# 图像尺寸配置 (英寸) - 增大尺寸以显示更多细节
FIGURE_SIZE = (24, 16)
# 字体和线条配置 - 为高分辨率优化
FONT_SIZES = {
    'title': 24,
    'subplot_title': 18,
    'axis_label': 16,
    'legend': 14,
    'tick_label': 12
}
LINE_WIDTH = 0.8  # 稍微增加线宽以适应高分辨率
# --- 配置结束 ---

# 全局变量用于时间戳推断
_base_date = None
_last_gngga_timestamp = None

def _gps_week_to_datetime(gps_week, ms_in_week):
    """将GPS周和周内毫秒数转换为UTC datetime对象。"""
    # GPS时间的起始点是 1980-01-06 00:00:00 UTC
    gps_epoch = datetime(1980, 1, 6)
    # 计算总时间偏移
    time_offset = timedelta(weeks=gps_week, milliseconds=ms_in_week)
    # GPS时间不考虑闰秒，对于大多数应用，这与UTC时间的差异可以忽略
    return gps_epoch + time_offset

def parse_gngga(line):
    """
    解析 $GNGGA 格式的单行日志，提取时间戳、解算状态和经纬度高程，然后转换为ECEF坐标。
    作为 BESTNAVXYZA 数据的备选方案。
    支持有时间戳和无时间戳两种格式。

    Args:
        line (str): 从原始数据文件中读取的一行文本。

    Returns:
        dict: 包含 'timestamp', 'x', 'y', 'z' 的字典，如果行不满足要求则返回 None。
    """
    global _last_gngga_timestamp, _base_date
    
    try:
        # 智能检测数据格式：有无时间戳前缀
        line = line.strip()
        if line.startswith('$GNGGA'):
            # 无时间戳格式：直接以$GNGGA开始
            msg = line
            timestamp_str = None
        else:
            # 有时间戳格式：时间戳 + 空格 + 数据
            parts = line.split(' ', 1)  # 只分割第一个空格
            if len(parts) < 2 or '$GNGGA' not in parts[1]:
                return None
            timestamp_str = parts[0]
            msg = parts[1]

        # 解析GNGGA数据字段
        gga_fields = msg.split(',')
        
        # 检查GPS状态 (字段6): 4=RTK固定，5=RTK浮动
        gps_quality = int(gga_fields[6]) if gga_fields[6] else 0
        
        # 构建时间戳
        if timestamp_str:
            # 有外部时间戳：使用ISO 8601格式
            dt_obj = datetime.fromisoformat(timestamp_str)
            if _base_date is None:
                _base_date = dt_obj.date()
        else:
            # 无外部时间戳：从GNGGA的UTC时间构建
            utc_time_str = gga_fields[1]  # 例如：043009.00
            if not utc_time_str:
                return None
            
            # 解析HHMMSS.SS格式
            if '.' in utc_time_str:
                time_part, frac_part = utc_time_str.split('.')
                microseconds = int(frac_part.ljust(6, '0')[:6])  # 转换为微秒
            else:
                time_part = utc_time_str
                microseconds = 0
            
            # 提取时分秒
            hours = int(time_part[:2])
            minutes = int(time_part[2:4])
            seconds = int(time_part[4:6])
            
            # 使用全局基准日期+UTC时间构建datetime
            if _base_date is None:
                print(f"警告: GNGGA ({utc_time_str}) 缺少基准日期，且之前未出现有效时间戳或BESTNAVXYZA记录。")
                print("将使用默认日期 2025-07-18。为确保准确，请保证数据文件包含时间戳或BESTNAVXYZA记录。")
                used_date = date(2025, 7, 18)
            else:
                used_date = _base_date
            
            dt_obj = datetime.combine(used_date, datetime.min.time().replace(
                hour=hours, minute=minutes, second=seconds, microsecond=microseconds))
        
        # 更新最近的GNGGA时间戳（用于旧版BESTNAVXYZA推断）
        _last_gngga_timestamp = dt_obj

        # 只接受RTK固定解 (状态4)，其他状态返回NaN以保持时间连续性
        if gps_quality != 4:
            return {'timestamp': dt_obj, 'x': np.nan, 'y': np.nan, 'z': np.nan}

        # 解析纬度 (字段2和3)
        lat_dm = float(gga_fields[2])  # 度分格式 ddmm.mmmm
        lat_deg = int(lat_dm / 100) + (lat_dm % 100) / 60  # 转换为十进制度
        if gga_fields[3] == 'S':
            lat_deg = -lat_deg

        # 解析经度 (字段4和5)
        lon_dm = float(gga_fields[4])  # 度分格式 dddmm.mmmm
        lon_deg = int(lon_dm / 100) + (lon_dm % 100) / 60  # 转换为十进制度
        if gga_fields[5] == 'W':
            lon_deg = -lon_deg

        # --- 关键修复: 正确计算椭球高度(HAE) ---
        # WGS84 ECEF坐标转换需要使用相对于椭球体的高度(HAE),
        # 而非GNGGA直接提供的海拔高度(MSL, field 9)。
        # HAE = 海拔高度(MSL) + 大地水准面差距(Geoid Undulation, field 11)
        alt_msl = float(gga_fields[9])
        
        # 检查并添加大地水准面差距
        if len(gga_fields) > 12 and gga_fields[11]:
            try:
                geoid_undulation = float(gga_fields[11])
                alt = alt_msl + geoid_undulation
            except (ValueError, TypeError):
                # 如果字段11无效，则回退为仅使用海拔高度
                alt = alt_msl
        else:
            alt = alt_msl

        # 转换为ECEF坐标
        x, y, z = pm.geodetic2ecef(lat_deg, lon_deg, alt, deg=True)

        return {'timestamp': dt_obj, 'x': x, 'y': y, 'z': z}
    except (IndexError, ValueError, TypeError):
        # 忽略所有无法解析的行
        return None

def parse_bestnavxyza(line):
    """
    解析 #BESTNAVXYZA 格式的单行日志，提取时间戳、解算状态和ECEF坐标。
    该函数会处理可能出现的解析错误，并根据解算状态筛选高质量数据。
    对于非固定解，返回包含NaN的记录以保持时间连续性。
    支持有时间戳和无时间戳两种格式，并能从无时间戳格式中解码GPS时间。

    Args:
        line (str): 从原始数据文件中读取的一行文本。

    Returns:
        dict: 包含 'timestamp', 'x', 'y', 'z' 的字典，如果行不满足要求则返回 None。
    """
    global _last_gngga_timestamp, _base_date
    
    try:
        # 智能检测数据格式：有无时间戳前缀
        line = line.strip()
        if line.startswith('#BESTNAVXYZA'):
            # 无时间戳格式：直接以#BESTNAVXYZA开始
            msg = line
            timestamp_str = None
        else:
            # 有时间戳格式：时间戳 + 空格 + 数据
            parts = line.split(' ', 1)  # 只分割第一个空格
            if len(parts) < 2 or '#BESTNAVXYZA' not in parts[1]:
                return None
            timestamp_str = parts[0]
            msg = parts[1]

        # 根据协议，数据在第一个分号之后
        parts = msg.split(';', 1)
        if len(parts) < 2:
            return None # 格式不完整，缺少分号
        
        header_part, data_part = parts
        data_fields = data_part.split(',')
        
        # 提取解算状态，位于第2个字段 (0-based 索引为 1)
        if len(data_fields) < 2:
            return None # 数据字段不完整
        pos_type = data_fields[1]
        
        # 构建时间戳
        if timestamp_str:
            # 有外部时间戳：使用ISO 8601格式
            dt_obj = datetime.fromisoformat(timestamp_str)
            if _base_date is None:
                _base_date = dt_obj.date()
        else:
            # 无外部时间戳：从BESTNAVXYZA的头部解码GPS周和周内毫秒
            header_fields = header_part.split(',')
            if len(header_fields) > 5:
                gps_week = int(header_fields[4])
                ms_in_week = int(header_fields[5])
                dt_obj = _gps_week_to_datetime(gps_week, ms_in_week)
                if _base_date is None:
                    _base_date = dt_obj.date()
            elif _last_gngga_timestamp is not None:
                # 备用方案：如果头部格式不符合预期，则回退到使用最近的GNGGA时间戳
                dt_obj = _last_gngga_timestamp
                print("警告: BESTNAVXYZA缺少时间戳且头部格式异常，回退至GNGGA时间进行推断。")
            else:
                # 如果没有GNGGA参考，使用固定基准时间
                print("警告: BESTNAVXYZA缺少时间戳且无任何参考，使用默认时间。")
                dt_obj = datetime.combine(_base_date or date(2025, 7, 18), datetime.min.time())

        # --- 关键修改: 对于非NARROW_INT解，返回包含NaN的记录以保持时间连续性 ---
        if pos_type != 'NARROW_INT':
            return {'timestamp': dt_obj, 'x': np.nan, 'y': np.nan, 'z': np.nan}

        # P-X, P-Y, P-Z 分别是字段3, 4, 5 (对应Python的0-based索引为2, 3, 4)
        if len(data_fields) < 5:
            return None # 坐标数据不完整
        x = float(data_fields[2])
        y = float(data_fields[3])
        z = float(data_fields[4])

        return {'timestamp': dt_obj, 'x': x, 'y': y, 'z': z}
    except (IndexError, ValueError, TypeError) as e:
        # 忽略所有无法解析的行
        # print(f"解析 BESTNAVXYZA 行时发生错误: {e} -> {line.strip()}")
        return None

def parse_line(line):
    """
    统一的行解析函数，优先尝试BESTNAVXYZA，如果失败则尝试GNGGA。
    
    Args:
        line (str): 从原始数据文件中读取的一行文本。
        
    Returns:
        dict: 包含 'timestamp', 'x', 'y', 'z' 的字典，如果行不满足要求则返回 None。
    """
    # 优先尝试解析BESTNAVXYZA（更高精度）
    result = parse_bestnavxyza(line)
    if result is not None:
        return result
    
    # 如果BESTNAVXYZA失败，尝试GNGGA作为备选
    result = parse_gngga(line)
    if result is not None:
        return result
    
    return None

def main():
    """
    主函数，执行以下步骤:
    1. 读取并解析RTK原始数据。
    2. 根据配置计算并建立一个稳定的参考基准点。
    3. 将所有ECEF坐标转换为相对于基准点的ENU坐标。
    4. 将位移单位转换为毫米。
    5. 生成并显示E、N、U三个方向的位移时序图。
    """
    global _last_gngga_timestamp, _base_date
    _last_gngga_timestamp = None  # 重置时间戳推断状态
    _base_date = None # 重置基准日期
    
    print(f"正在从 '{RAW_DATA_FILE}' 文件中读取数据...")
    try:
        with open(RAW_DATA_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"错误: 数据文件 '{RAW_DATA_FILE}' 未找到。请检查文件名和路径是否正确。")
        return

    # 解析文件中的每一行，提取ECEF数据
    ecef_data = [parse_line(line) for line in lines]
    ecef_data = [d for d in ecef_data if d is not None]

    if not ecef_data:
        print("错误: 在文件中未找到有效的 RTK 数据（支持 '#BESTNAVXYZA' 和 '$GNGGA' 格式）。")
        return

    # 使用pandas DataFrame进行数据处理
    df = pd.DataFrame(ecef_data)
    df = df.set_index('timestamp')
    df.sort_index(inplace=True)

    # 统计有效固定解的数量
    valid_count = df[['x', 'y', 'z']].notna().all(axis=1).sum()
    total_count = len(df)
    print(f"成功解析 {total_count} 条记录，其中 {valid_count} 条为高质量固定解记录。")

    # --- 步骤 1: 建立参考基准点 (只使用有效的固定解数据) ---
    if df.empty:
        print("数据为空，无法处理。")
        return
        
    start_time = df.index[0]
    ref_start_time = start_time + timedelta(seconds=STABILIZATION_SECONDS)
    ref_end_time = ref_start_time + timedelta(seconds=AVERAGING_SECONDS)

    print(f"数据起始时间: {start_time}")
    print(f"基准点计算窗口: 从 {ref_start_time} 到 {ref_end_time}")

    # 筛选出用于计算基准的数据 (只使用有效的固定解)
    ref_df = df[(df.index >= ref_start_time) & (df.index <= ref_end_time)]
    ref_df_valid = ref_df.dropna()  # 只使用有效数据计算基准点

    if ref_df_valid.empty:
        print("错误: 没有足够的有效固定解数据来建立参考基准点。")
        print(f"数据时间范围从 {df.index.min()} 到 {df.index.max()}。")
        print(f"基准点计算窗口为 {ref_start_time} 至 {ref_end_time}。")
        print("请检查：\n1. 数据文件的时间跨度是否足够。\n2. STABILIZATION_SECONDS 和 AVERAGING_SECONDS 参数是否过大。\n3. 数据质量是否过差，导致窗口内无固定解。")
        return

    # 计算基准点的平均ECEF坐标 (x0, y0, z0)
    x0, y0, z0 = ref_df_valid[['x', 'y', 'z']].mean()

    # 将ECEF基准点转换为大地坐标 (纬度, 经度, 高程)，这将是ENU坐标系的原点
    lat0, lon0, h0 = pm.ecef2geodetic(x0, y0, z0, deg=True)
    print(f"已建立参考基准点 (大地坐标): 纬度={lat0:.8f}°, 经度={lon0:.8f}°, 高程={h0:.4f} m")

    # --- 步骤 2: 计算每个点相对于基准点的ECEF差值向量 ---
    df['dx'] = df['x'] - x0
    df['dy'] = df['y'] - y0
    df['dz'] = df['z'] - z0

    # --- 步骤 3: 将ECEF差值向量转换为ENU坐标 (只对有效数据进行转换) ---
    def convert_to_enu(row):
        if pd.isna(row['dx']) or pd.isna(row['dy']) or pd.isna(row['dz']):
            return pd.Series([np.nan, np.nan, np.nan])
        else:
            return pd.Series(pm.ecef2enuv(row['dx'], row['dy'], row['dz'], lat0, lon0, deg=True))
    
    enu_coords = df.apply(convert_to_enu, axis=1)
    enu_coords.columns = ['e', 'n', 'u']

    # --- 步骤 4: 将单位从米转换为毫米 ---
    df['e_mm'] = enu_coords['e'] * 1000
    df['n_mm'] = enu_coords['n'] * 1000
    df['u_mm'] = enu_coords['u'] * 1000

    # --- 新增步骤: 计算二维和三维空间位移 ---
    print("正在计算二维和三维空间位移...")
    df['displacement_2d_mm'] = np.sqrt(df['e_mm']**2 + df['n_mm']**2)
    df['displacement_3d_mm'] = np.sqrt(df['e_mm']**2 + df['n_mm']**2 + df['u_mm']**2)

    print("数据处理完成，正在生成高分辨率可视化图表...")

    # --- 步骤 5: 高分辨率可视化 (为MLLM分析优化) ---
    # 设置matplotlib参数以优化高分辨率输出
    plt.rcParams.update({
        'figure.dpi': 100,  # 显示DPI
        'savefig.dpi': 600,  # 保存DPI
        'font.size': FONT_SIZES['tick_label'],
        'axes.titlesize': FONT_SIZES['subplot_title'],
        'axes.labelsize': FONT_SIZES['axis_label'],
        'xtick.labelsize': FONT_SIZES['tick_label'],
        'ytick.labelsize': FONT_SIZES['tick_label'],
        'legend.fontsize': FONT_SIZES['legend']
    })
    
    # --- 5合1总图 ---
    fig, axes = plt.subplots(5, 1, figsize=(FIGURE_SIZE[0], 28), sharex=False)
    fig.suptitle('Scaffolding Displacement Monitoring (ENU & Spatial Displacements)', 
                 fontsize=FONT_SIZES['title'], y=0.96, fontweight='bold')

    # 绘制 东(E) 方向位移 - 高分辨率优化
    axes[0].plot(df.index, df['e_mm'], label='East (E) Displacement', 
                 color='#FF4444', linestyle='-', linewidth=LINE_WIDTH, alpha=0.8)
    axes[0].set_ylabel('Displacement (mm)', fontsize=FONT_SIZES['axis_label'])
    axes[0].set_title('East-West Displacement (E)', fontsize=FONT_SIZES['subplot_title'], pad=20)
    axes[0].grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    axes[0].legend(fontsize=FONT_SIZES['legend'])
    axes[0].tick_params(axis='both', which='major', labelsize=FONT_SIZES['tick_label'])

    # 绘制 北(N) 方向位移 - 高分辨率优化
    axes[1].plot(df.index, df['n_mm'], label='North (N) Displacement', 
                 color='#44AA44', linestyle='-', linewidth=LINE_WIDTH, alpha=0.8)
    axes[1].set_ylabel('Displacement (mm)', fontsize=FONT_SIZES['axis_label'])
    axes[1].set_title('North-South Displacement (N)', fontsize=FONT_SIZES['subplot_title'], pad=20)
    axes[1].grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    axes[1].legend(fontsize=FONT_SIZES['legend'])
    axes[1].tick_params(axis='both', which='major', labelsize=FONT_SIZES['tick_label'])

    # 绘制 天(U) 方向位移 - 高分辨率优化
    axes[2].plot(df.index, df['u_mm'], label='Up (U) Displacement', 
                 color='#4444FF', linestyle='-', linewidth=LINE_WIDTH, alpha=0.8)
    axes[2].set_ylabel('Displacement (mm)', fontsize=FONT_SIZES['axis_label'])
    axes[2].set_title('Vertical Displacement (U)', fontsize=FONT_SIZES['subplot_title'], pad=20)
    axes[2].grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    axes[2].legend(fontsize=FONT_SIZES['legend'])
    axes[2].tick_params(axis='both', which='major', labelsize=FONT_SIZES['tick_label'])

    # 新增: 绘制 二维(2D) 空间位移
    axes[3].plot(df.index, df['displacement_2d_mm'], label='2D Planar Displacement', 
                 color='#9933FF', linestyle='-', linewidth=LINE_WIDTH, alpha=0.8) # 紫色
    axes[3].set_ylabel('Displacement (mm)', fontsize=FONT_SIZES['axis_label'])
    axes[3].set_title('2D Planar Displacement (sqrt(E² + N²))', fontsize=FONT_SIZES['subplot_title'], pad=20)
    axes[3].grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    axes[3].legend(fontsize=FONT_SIZES['legend'])
    axes[3].tick_params(axis='both', which='major', labelsize=FONT_SIZES['tick_label'])

    # 新增: 绘制 三维(3D) 空间位移
    axes[4].plot(df.index, df['displacement_3d_mm'], label='3D Spatial Displacement', 
                 color='#FF6600', linestyle='-', linewidth=LINE_WIDTH, alpha=0.8) # 橙色
    axes[4].set_ylabel('Displacement (mm)', fontsize=FONT_SIZES['axis_label'])
    axes[4].set_xlabel('Timestamp', fontsize=FONT_SIZES['axis_label'])
    axes[4].set_title('3D Spatial Displacement (sqrt(E² + N² + U²))', fontsize=FONT_SIZES['subplot_title'], pad=20)
    axes[4].grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
    axes[4].legend(fontsize=FONT_SIZES['legend'])
    axes[4].tick_params(axis='both', which='major', labelsize=FONT_SIZES['tick_label'])

    # --- X轴刻度优化 (为每个子图应用) ---
    for ax in axes:
        # 设置主刻度为每5分钟一个
        ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
        # 设置主刻度的格式为 '时:分'
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        # 为更密集的视图添加次刻度（每分钟）
        ax.xaxis.set_minor_locator(mdates.MinuteLocator())
        # 旋转标签以防重叠
        for label in ax.get_xticklabels(which='major'):
            label.set(rotation=45, horizontalalignment='right')

    # 自动格式化x轴日期显示，以避免标签重叠
    # fig.autofmt_xdate(rotation=45, ha='right') # 由上面的循环手动控制
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    # --- 多格式高分辨率保存 ---
    print("正在保存5合1组合图像文件...")
    output_dir = 'ENU-RESULT'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出目录: '{output_dir}'")
        
    base_filename = 'scaffolding_displacement_summary'
    
    for format_name, config in OUTPUT_FORMATS.items():
        output_filename = os.path.join(output_dir, f"{base_filename}.{format_name}")
        
        # 设置保存参数
        save_kwargs = {
            'bbox_inches': 'tight',
            'pad_inches': 0.2,
            'facecolor': 'white',
            'edgecolor': 'none'
        }
        
        # 为不同格式设置特定参数
        if config['dpi'] is not None:
            save_kwargs['dpi'] = config['dpi']
        
        # 保存文件
        plt.savefig(output_filename, format=format_name, **save_kwargs)
        print(f"✓ {config['description']} 已保存为 '{output_filename}'")
    
    # plt.close(fig) # 已移除：关闭组合图，确保plt.show()可以显示它

    # --- 步骤 6: 为每个分量单独生成并保存高分辨率图像 ---
    print("\n正在为每个位移分量生成并保存单独的高分辨率图像...")
    
    SINGLE_PLOT_FIG_SIZE = (24, 10)
    plot_configs = {
        'e_mm': {'title': 'East-West Displacement (E)', 'color': '#FF4444', 'filename': 'displacement_e'},
        'n_mm': {'title': 'North-South Displacement (N)', 'color': '#44AA44', 'filename': 'displacement_n'},
        'u_mm': {'title': 'Vertical Displacement (U)', 'color': '#4444FF', 'filename': 'displacement_u'},
        'displacement_2d_mm': {'title': '2D Planar Displacement (sqrt(E² + N²))', 'color': '#9933FF', 'filename': 'displacement_2d'},
        'displacement_3d_mm': {'title': '3D Spatial Displacement (sqrt(E² + N² + U²))', 'color': '#FF6600', 'filename': 'displacement_3d'}
    }

    for column, config in plot_configs.items():
        plt.figure(figsize=SINGLE_PLOT_FIG_SIZE)
        
        ax_single = plt.gca()
        ax_single.plot(df.index, df[column], label=config['title'], color=config['color'], linestyle='-', linewidth=LINE_WIDTH, alpha=0.8)
        
        ax_single.set_title(config['title'], fontsize=FONT_SIZES['subplot_title'], pad=20)
        ax_single.set_ylabel('Displacement (mm)', fontsize=FONT_SIZES['axis_label'])
        ax_single.set_xlabel('Timestamp', fontsize=FONT_SIZES['axis_label'])
        ax_single.grid(True, linestyle='--', alpha=0.3, linewidth=0.5)
        ax_single.legend(fontsize=FONT_SIZES['legend'])
        ax_single.tick_params(axis='both', which='major', labelsize=FONT_SIZES['tick_label'])

        ax_single.xaxis.set_major_locator(mdates.MinuteLocator(interval=5))
        ax_single.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax_single.xaxis.set_minor_locator(mdates.MinuteLocator())
        plt.setp(ax_single.get_xticklabels(), rotation=45, ha='right')

        plt.tight_layout(pad=1.5)

        for format_name, format_config in OUTPUT_FORMATS.items():
            output_filename = os.path.join(output_dir, f"{config['filename']}.{format_name}")
            save_kwargs = {'bbox_inches': 'tight', 'pad_inches': 0.2, 'facecolor': 'white', 'edgecolor': 'none'}
            if format_config['dpi'] is not None:
                save_kwargs['dpi'] = format_config['dpi']
            plt.savefig(output_filename, format=format_name, **save_kwargs)
            print(f"✓ 单独图像 ({format_config['description']}) 已保存为 '{output_filename}'")
        
        # plt.close() # 已移除：完成保存后不关闭当前图像，确保plt.show()可以显示它

    # 添加数据质量统计信息
    valid_ratio = (valid_count / total_count) * 100 if total_count > 0 else 0
    print(f"\n📊 数据质量统计:")
    print(f"   总记录数: {total_count}")
    print(f"   固定解记录数: {valid_count}")
    print(f"   数据质量率: {valid_ratio:.1f}%")
    
    # 计算位移统计
    if not df['e_mm'].isna().all():
        print(f"\n📏 位移统计 (毫米):")
        print(f"   东西方向 (E): 范围 [{df['e_mm'].min():.2f}, {df['e_mm'].max():.2f}], 标准差 {df['e_mm'].std():.2f}")
        print(f"   南北方向 (N): 范围 [{df['n_mm'].min():.2f}, {df['n_mm'].max():.2f}], 标准差 {df['n_mm'].std():.2f}")
        print(f"   垂直方向 (U): 范围 [{df['u_mm'].min():.2f}, {df['u_mm'].max():.2f}], 标准差 {df['u_mm'].std():.2f}")
        print(f"   二维水平位移: 范围 [{df['displacement_2d_mm'].min():.2f}, {df['displacement_2d_mm'].max():.2f}], 标准差 {df['displacement_2d_mm'].std():.2f}")
        print(f"   三维空间位移: 范围 [{df['displacement_3d_mm'].min():.2f}, {df['displacement_3d_mm'].max():.2f}], 标准差 {df['displacement_3d_mm'].std():.2f}")
    
    print(f"\n🎯 图像已针对MLLM分析进行优化，建议使用SVG格式进行细节分析。")
    
    # 显示图像
    print("\n所有图像已保存。在脚本执行结束后，将显示所有生成的交互式图表。")
    plt.show()

if __name__ == '__main__':
    main() 