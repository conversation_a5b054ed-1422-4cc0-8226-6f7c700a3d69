$GNGGA
例：$GNGGA,034533.00,2215.85063929,N,11415.04101820,E,4,18,1.2,7.3242,M,-1.7442,M,1.0,4095*41
字段0： $GNGGA，语句ID，表明该语句为Global Positioning System Fix Data（GGA）GPS定位信息；示例: $GNGGA
字段1： UTC 时间，hhmmss.sss，时分秒格式；示例: 034533.00
字段2： 纬度ddmm.mmmm，度分格式；示例: 2215.85063929
字段3： 纬度N（北纬）或S（南纬）；示例: N
字段4： 经度dddmm.mmmm，度分格式；示例: 11415.04101820
字段5： 经度E（东经）或W（西经）；示例: E
字段6： GPS状态，0=未定位，1=单点定位，2=伪距/SBAS，3=无效PPS，4=RTK固定，5=RTK浮动，6=正在估算 7=手动启动基准站，8=RTK宽巷解，9=伪距（诺瓦泰615）；示例: 4 (RTK固定)
字段7： 正在使用的卫星数量；示例: 18
字段8： HDOP水平精度因子；示例: 1.2
字段9： 海拔高度；示例: 7.3242
字段10： 海拔高度单位，M表示米；示例: M
字段11： 地球椭球面相对大地水准面的高度；示例: -1.7442
字段12： 高度单位，M表示米；示例: M
字段13： 差分时间（从最近一次接收到差分信号开始的秒数）；示例: 1.0
字段14： 差分站ID号；示例: 4095
字段15： 校验值；示例: *41

$GNGST
例：$GNGST,034533.00,2.08,0.04,0.01,139.0725,0.035,0.031,0.047*48
字段0： $GPGST，语句ID，表明该句为（GST）伪距误差统计；示例: $GNGST
字段1： UTC时间，hhmmss.sss格式；示例: 034533.00
字段2： 用于导航处理的输入范围的标准差的均方根；示例: 2.08
字段3： 误差椭圆长半轴的标准差（米）；示例: 0.04
字段4： 误差椭圆短半轴的标准差（米）；示例: 0.01
字段5： 误差椭圆长半轴的方向（度）；示例: 139.0725
字段6： 纬度误差的标准差（米）；示例: 0.035
字段7： 经度误差的标准差（米）；示例: 0.031
字段8： 高度误差的标准差（米）；示例: 0.047
字段9： 校验值；示例: *48

#BESTNAVXYZA
例：#BESTNAVXYZA,93,GPS,FINE,2375,445551000,0,0,18,11;SOL_COMPUTED,NARROW_INT,-2425551.9792,5384354.1705,2401512.4661,0.0379,0.0424,0.0346,SOL_COMPUTED,DOPPLER_VELOCITY,-0.0025,0.0041,0.0001,0.0173,0.0225,0.0194,"4095",0.000,2.000,0.000,28,18,18,16,0,01,7,f3*bd5965e6
字段0： BESTNAVXYZ header — 消息头；示例: #BESTNAVXYZA,93,GPS,FINE,2375,445551000,0,0,18,11
字段1： P-sol status — 位置解状态；示例: SOL_COMPUTED
字段2： pos type — 位置类型；示例: NARROW_INT
字段3： P-X — X 轴坐标，单位 m；示例: -2425551.9792
字段4： P-Y — Y 轴坐标，单位 m；示例: 5384354.1705
字段5： P-Z — Z 轴坐标，单位 m；示例: 2401512.4661
字段6： P-X σ — X 轴坐标标准差，单位 m；示例: 0.0379
字段7： P-Y σ — Y 轴坐标标准差，单位 m；示例: 0.0424
字段8： P-Z σ — Z 轴坐标标准差，单位 m；示例: 0.0346
字段9： V-sol status — 速度解状态；示例: SOL_COMPUTED
字段10： vel type — 速度类型；示例: DOPPLER_VELOCITY
字段11： V-X — X 轴速度，单位 m/s；示例: -0.0025
字段12： V-Y — Y 轴速度，单位 m/s；示例: 0.0041
字段13： V-Z — Z 轴速度，单位 m/s；示例: 0.0001
字段14： V-X σ — X 轴速度标准差，单位 m/s；示例: 0.0173
字段15： V-Y σ — Y 轴速度标准差，单位 m/s；示例: 0.0225
字段16： V-Z σ — Z 轴速度标准差，单位 m/s；示例: 0.0194
字段17： stn ID — 基站 ID；示例: "4095"
字段18： V-latency — 速度延迟（秒）；示例: 0.000
字段19： diff_age — 差分龄期（s）；示例: 2.000
字段20： sol_age — 解的龄期（s）；示例: 0.000
字段21： #SVs — 跟踪的卫星数；示例: 28
字段22： #solnSVs — 在解算中使用的卫星数；示例: 18
字段23： #ggL1 — L1/G1/B1 信号参与解算的卫星数；示例: 18
字段24： #solnMultiSVs — L1/G1/B1/E1 信号参与解算的卫星数；示例: 16
字段25： Reserved — 保留；示例: 0
字段26： ext sol stat — 扩展解状态；示例: 01
字段27： Galileo & BDS3 sig mask — Galileo 和 BDS3 使用的信号掩码；示例: 7
字段28： GPS/GLONASS/BDS2 sig mask — GPS、GLONASS 和 BDS2 使用的信号掩码；示例: f3
字段29： xxxx — 32-位 CRC 校验；示例: *bd5965e6
