python -m vllm.entrypoints.openai.api_server \
    --model "mlabonne/gemma-3-12b-it-abliterated-v2" \
    --tensor-parallel-size 2 \
    --host 0.0.0.0 \
    --port 18888 \
    --gpu-memory-utilization 0.95
    --max-model-len 8192

vllm serve /home/<USER>/Downloads/gemma-3-27b-it-abliterated-v2.q4_k_m.gguf --trust-remote-code --tensor-parallel-size 2 --port 18888 --enable-prefix-caching --enable-chunked-prefill --max-model-len 4096 --gpu-memory-utilization 0.95 --max-num-seqs 16 --disable-log-stats --disable-log-requests


export HF_TOKEN=*************************************
vllm serve RedHatAI/gemma-3-27b-it-quantized.w4a16 --max-model-len 8192 --trust-remote-code --tensor-parallel-size 2 --port 18888 --enable-prefix-caching --enable-chunked-prefill --max_num_seqs 2 --gpu-memory-utilization 0.85