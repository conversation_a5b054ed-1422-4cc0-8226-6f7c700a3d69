import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from scipy.signal import square, sawtooth

# ================== CONFIGURATION ==================
# 输入文件路径
INPUT_CSV = 'CSV_output/BESTNAVXYZA.csv'
# 输出文件路径
OUTPUT_DIR = 'CSV_output'
ANOMALOUS_CSV_NAME = 'BESTNAVXYZA_anomalous.csv'
PLOT_IMAGE_NAME = 'injected_anomaly_plot.png'

# 要注入异常的字段
TARGET_COLUMN = 'P-Y' 

# --- 新增：控制异常类型和范围 ---
ANOMALY_TYPE = 'square'  # 可选 'triangle', 'square', 'sawtooth'
ANOMALY_START_FRAC = 0.3 # 异常注入的起始位置（占总长度的百分比, 0.0 to 1.0）
ANOMALY_LENGTH_FRAC = 0.2 # 异常注入的持续长度（占总长度的百分比, 0.0 to 1.0）
# ---------------------------------

# 异常波形参数
ANOMALY_FREQ = 0.05  # 异常波形的频率
ANOMALY_COEF = 1.5   # 异常波形的幅度系数
NOISE_AMP = 0.1    # 噪声幅度
# =================================================

# ===== Step 1: 从 generator.py 复制相关的异常生成函数 =====

def triangle_wave(length, freq=0.04, coef=1.5, offset=0.0, noise_amp=0.05):
    """生成三角波"""
    timestamp = np.arange(length)
    value = 2 * np.abs((timestamp * freq) % 1 - 0.5) - 1 
    if noise_amp != 0:
        noise = np.random.normal(0, 1, length)
        value = value + noise_amp * noise
    value = coef * value + offset
    return value

def square_wave(length, freq=0.04, coef=1.5, offset=0.0, noise_amp=0.05):
    """生成方波"""
    timestamp = np.arange(length)
    value = square(2 * np.pi * freq * timestamp) 
    if noise_amp != 0:
        noise = np.random.normal(0, 1, length)
        value = value + noise_amp * noise
    value = coef * value + offset
    return value

def sawtooth_wave(length, freq=0.04, coef=1.5, offset=0.0, noise_amp=0.05):
    """生成锯齿波"""
    timestamp = np.arange(length)
    value = sawtooth(2 * np.pi * freq * timestamp) 
    if noise_amp != 0:
        noise = np.random.normal(0, 1, length)
        value = value + noise_amp * noise
    value = coef * value + offset
    return value

def plot_injected_anomaly(original_df, anomalous_df, target_columns, output_path):
    """
    可视化原始数据和注入异常后的数据
    """
    num_dims = len(target_columns)
    fig, axes = plt.subplots(num_dims, 1, figsize=(12, 2 * num_dims), sharex=True)
    if num_dims == 1:
        axes = [axes]

    for i, col in enumerate(target_columns):
        # 绘制原始数据
        axes[i].plot(original_df.index, original_df[col], label=f'Original {col}', color='blue', alpha=0.7)
        # 绘制异常数据
        axes[i].plot(anomalous_df.index, anomalous_df[col], label=f'Anomalous {col}', color='red', linestyle='--')
        
        # 标记出发生变化区域
        diff = original_df[col] != anomalous_df[col]
        axes[i].fill_between(anomalous_df.index, axes[i].get_ylim()[0], axes[i].get_ylim()[1], 
                             where=diff, color='orange', alpha=0.3, label='Anomaly Injected Zone')

        axes[i].set_ylabel(col)
        axes[i].legend()
        axes[i].grid(True, which='both', linestyle='--', linewidth=0.5)

    axes[-1].set_xlabel('Timestamp Index')
    fig.suptitle('Time Series with Injected Anomalies', fontsize=16)
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    
    # 保存图像
    fig.savefig(output_path)
    plt.close()
    print(f"Plot saved to {output_path}")

def main():
    # 确保输出目录存在
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    # ===== Step 2: 加载你的数据 =====
    try:
        df = pd.read_csv(INPUT_CSV)
    except FileNotFoundError:
        print(f"Error: Input file not found at {INPUT_CSV}")
        return

    print(f"Successfully loaded data from {INPUT_CSV}. Shape: {df.shape}")
    
    # 复制一份原始数据用于注入异常
    df_anomalous = df.copy()
    
    # 定义要处理的XYZ坐标列
    xyz_columns = ['P-X', 'P-Y', 'P-Z']
    
    # ===== Step 3: 注入异常 =====
    data_length = len(df_anomalous)

    # 定义异常类型到函数的映射
    anomaly_functions = {
        'triangle': triangle_wave,
        'square': square_wave,
        'sawtooth': sawtooth_wave
    }

    if ANOMALY_TYPE not in anomaly_functions:
        print(f"错误: 无效的异常类型 '{ANOMALY_TYPE}'. 请从 {list(anomaly_functions.keys())} 中选择。")
        return
    
    # 计算注入范围的索引
    start_index = int(data_length * ANOMALY_START_FRAC)
    anomaly_length = int(data_length * ANOMALY_LENGTH_FRAC)
    end_index = start_index + anomaly_length

    if end_index > data_length:
        end_index = data_length
        print(f"警告: 异常范围超出数据总长度，自动调整结束位置为 {end_index}")

    print(f"准备向 '{TARGET_COLUMN}' 列的索引 {start_index} 到 {end_index} 范围内注入 '{ANOMALY_TYPE}' 型异常。")
    
    # 获取原始数据的均值和标准差，以便让异常波形处于相似的数值范围
    original_mean = df_anomalous[TARGET_COLUMN].mean()
    original_std = df_anomalous[TARGET_COLUMN].std()

    # 选择异常函数
    selected_anomaly_func = anomaly_functions[ANOMALY_TYPE]
    
    # 生成与原始数据等长的完整异常波形，以确保频率等特性正确
    full_anomaly_data = selected_anomaly_func(
        length=data_length, 
        freq=ANOMALY_FREQ, 
        coef=original_std * ANOMALY_COEF, # 使用原始数据的标准差来缩放幅度
        offset=original_mean,           # 使用原始数据的均值作为偏移
        noise_amp=original_std * NOISE_AMP  # 噪声也基于原始数据的标准差
    )
    
    # 仅将指定范围的数据替换为异常波形
    # 使用 .loc 进行更安全的赋值
    df_anomalous.loc[start_index:end_index-1, TARGET_COLUMN] = full_anomaly_data[start_index:end_index]
    
    print("异常注入完成。")

    # ===== Step 4: 保存结果 =====
    output_csv_path = os.path.join(OUTPUT_DIR, ANOMALOUS_CSV_NAME)
    df_anomalous.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
    print(f"Anomalous data saved to {output_csv_path}")

    # ===== Step 5: 可视化结果 =====
    output_plot_path = os.path.join(OUTPUT_DIR, PLOT_IMAGE_NAME)
    plot_injected_anomaly(df, df_anomalous, xyz_columns, output_plot_path)

if __name__ == '__main__':
    main()
