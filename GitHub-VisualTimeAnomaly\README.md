# Can Multimodal LLMs Perform Time Series Anomaly Detection?
This repo includes the official code and datasets for paper ["Can Multimodal LLMs Perform Time Series Anomaly Detection?"](https://arxiv.org/abs/2502.17812)

## 🕵️‍♂️ VisualTimeAnomaly
<div align="center">
<img src="teaser.png" style="width: 100%;height: 100%">
</div>

<p align="center"><b><font size="30">Left: the workflow of VisualTimeAnomaly. Right: the performance comparison across various setting.</font></b></p>

## 🏆 Contributions
- The first comprehensive benchmark for multimodal LLMs (MLLMs) in time series anomaly detection (TSAD), covering diverse scenarios (univariate, multivariate, irregular) and varying anomaly granularities (point-, range-, variate-wise).
- Several critical insights significantly advance the understanding of both MLLMs and TSAD.
- We construct a large-scale dataset including 12.4k time series images, and release the dateset and code to foster future research.

## 🔎 Findings
- MLLMs detect range- and variate-wise anomalies more effectively than point-wise anomalies;
- MLLMs are highly robust to irregular time series, even with 25% of the data missing;
- Open-source MLLMs perform comparably to proprietary models in TSAD. While open-source MLLMs excel on univariate time series, proprietary MLLMs demonstrate superior effectiveness on multivariate time series.

## ⚙️ Getting Started
### Environment
* python               3.10.14
* torch                2.4.1
* numpy                1.26.4
* transformers         4.49.0.dev0
* huggingface-hub      0.24.7
* openai               1.44.0
* google-generativeai  0.8.3

### Dataset
Enter `src` folder.

If you want to generate all datasets, execute the below script:

`./generator.sh`

If you want to generate a specific dataset, execute the below script:

`python generator.py --category $category --scenario $scenario --anomaly_type $anomaly_type --num_ts $num_ts`.

For example, generate 100 univaraite time series images for global anomalies:

`python generator.py --category synthetic --scenario univariate --anomaly_type global --num_ts 100`

### Run 
Enter `src` folder.

If you want to run MLLMs on all datasets, execute the below script:

`./test.sh`

If you want to run a MLLM on a specific dataset, execute the below script:

`python main.py --category $category --scenario $scenario --model_name $model_name --data $data`

For example, run GPT-4o on univaraite time series scenario with global anomalies:

`python main.py --category synthetic --scenario univariate --model_name gpt-4o --data global`

## Acknowledgement
We sincerely appreciate the following github repo for the code base and datasets:

https://github.com/Rose-STL-Lab/AnomLLM

https://github.com/datamllab/tods/tree/benchmark

## 📝 Citation  
If you find our work useful, please cite the below paper:
```bibtex
@article{xu2025can,
  title={Can Multimodal LLMs Perform Time Series Anomaly Detection?},
  author={Xu, Xiongxiao and Wang, Haoran and Liang, Yueqing and Yu, Philip S and Zhao, Yue and Shu, Kai},
  journal={arXiv preprint arXiv:2502.17812},
  year={2025}
}
```

## 📂 文件作用说明

### 🔧 核心功能文件

**`src/main.py`** - 主程序入口
- 统一管理不同多模态大语言模型的加载和调用（GPT、Gemini、LLaVA、Qwen）
- 实现带重试机制的异常检测流程
- 处理多种场景：单变量、多变量、不规则时间序列
- 支持不同异常粒度：点级、范围级、变量级异常

**`src/dataloader.py`** - 数据加载器
- `TSIDataset`类：时间序列图像数据集的核心实现
- 支持加载预处理的时间序列数据和对应的异常标签
- 提供few-shot学习的数据采样功能
- 处理不规则时间序列的缺失数据索引

**`src/generator.py`** - 数据生成器
- 生成多种类型的合成时间序列：正弦波、三角波、方波、锯齿波、随机游走
- 支持多种异常类型：全局、上下文、季节性、趋势、形状异常
- 支持单变量和多变量场景的数据生成
- 生成不规则时间序列（随机缺失数据）
- 将时间序列可视化为图像并保存

### 🌐 API接口文件

**`src/openai_api.py`** - OpenAI/GPT模型接口
- 加载GPT-4o、GPT-4o-mini等模型
- 处理Azure OpenAI服务调用
- 提供统一的GPT系列模型调用接口

**`src/gemini_api.py`** - Google Gemini模型接口
- 支持Gemini-1.5-pro、Gemini-1.5-flash模型
- 实现OpenAI格式到Gemini API格式的转换
- 处理图像编码和安全设置

**`src/llava_api.py`** - LLaVA开源模型接口
- 支持LLaMA3-LLaVA-Next系列模型（8B、72B）
- 本地模型推理和GPU内存优化
- 实现4bit量化加载以节省显存

**`src/qwen_api.py`** - 通义千问模型接口
- 支持Qwen2-VL系列视觉语言模型（7B、72B）
- 处理视觉信息和文本信息的联合处理
- 格式转换和本地推理优化

### ⚙️ 配置和提示词文件

**`src/config.py`** - 配置管理
- 创建API配置，目前支持零样本视觉检测
- 统一管理不同推理方式的配置参数

**`src/prompt.py`** - 提示词模板
- 定义三种异常检测任务的提示词：
  - `PROMPT_POINT`：点级异常检测
  - `PROMPT`：范围级异常检测  
  - `PROMPT_VARIATE`：变量级异常检测（多变量）
- 处理图像编码和消息格式构建
- 根据数据类型自动选择合适的提示词

**`src/credentials.yml`** - API密钥配置
- 存储各种模型的API密钥和服务端点
- 支持GPT和Gemini的配置信息

### 🔬 工具和评估文件

**`src/utils.py`** - 工具函数集合
- 数据格式转换：点↔向量、区间↔向量、ID↔向量
- 时间序列可视化：单变量和多变量绘图函数
- 图像处理和Base64编码工具
- 异常标签的格式转换工具

**`src/result_agg.py`** - 结果聚合和评估
- 计算多种评估指标：精确率、召回率、F1分数
- 支持Affiliation指标（时间序列异常检测专用评估方法）
- 生成LaTeX格式的结果表格
- 跨不同场景和模型的性能比较和统计分析

### 🚀 自动化脚本文件

**`src/generator.sh`** - 数据生成自动化脚本
- 批量生成所有实验场景的数据
- 覆盖合成数据：单变量、多变量、不规则序列
- 覆盖半合成数据：基于真实数据集的扩展
- 支持不同维度、缺失比例、异常类型的组合

**`src/test.sh`** - 模型测试自动化脚本
- 批量测试所有模型在所有数据集上的性能
- 8个多模态大语言模型 × 多种数据场景的完整评估
- 自动化的端到端测试流程

### 📄 其他文件

**`LICENSE`** - MIT开源许可证
- 项目的开源许可协议

**`teaser.png`** - 项目展示图
- 展示VisualTimeAnomaly的工作流程和性能比较
